# Fitur Export Excel - Dashboard Kang Ider

## Overview
Fitur export Excel memungkinkan pengguna untuk mengunduh laporan lengkap dashboard dalam format Excel (.xlsx) dengan data yang terorganisir dalam multiple sheets.

## 🎯 Fitur Utama

### 1. **Tombol Export Excel**
- **Lokasi**: Header dashboard, di bagian Action Buttons
- **Styling**: Tombol hijau dengan icon download
- **Responsif**: Menyesuaikan dengan ukuran layar

### 2. **Multi-Sheet Excel File**
File Excel yang dihasilkan terdiri dari 5 sheet:

#### **Sheet 1: <PERSON><PERSON><PERSON>**
```
LAPORAN DASHBOARD KANG IDER

Periode: 01/01/2024 - 31/01/2024
Kang Ider: Semua
Tanggal Export: 23/07/2025 10:30:15

RINGKASAN KEUANGAN
Total Pembayaran Cash: Rp 1,500,000
Total Pembayaran QRIS: Rp 2,300,000
Total Pendapatan: Rp 3,800,000
Total Pengeluaran Operasional: Rp 200,000
Total Penggunaan Internal: Rp 150,000
Total Pengeluaran: Rp 350,000
Omset Bersih: Rp 3,450,000
```

#### **Sheet 2: Penjualan**
| ID | Tanggal | Total Harga | Metode Bayar | Status | Kang Ider |
|----|---------|-------------|--------------|--------|-----------|
| 1 | 23/07/2025 10:30 | 25000 | Cash | completed | Ahmad |
| 2 | 23/07/2025 11:15 | 30000 | QRIS | completed | Budi |

#### **Sheet 3: Menu & Stock**
| Menu | Harga | Stock Awal | Terjual | Stock Akhir | Nilai Penjualan | Status Stock |
|------|-------|------------|---------|-------------|-----------------|--------------|
| Kopi Hitam | 5000 | 50 | 30 | 20 | 150000 | Cukup (60% terjual) |
| Kopi Susu | 8000 | 40 | 35 | 5 | 280000 | Sedikit (88% terjual) |

#### **Sheet 4: Pengeluaran Operasional** (jika ada data)
| ID | Tanggal | Waktu | Nama Pengeluaran | Kategori | Jumlah | Keterangan | Kang Ider |
|----|---------|-------|------------------|----------|--------|------------|-----------|
| 1 | 23/07/2025 | 14:30 | Parkir | Transport | 5000 | Bayar parkir | Ahmad |

#### **Sheet 5: Penggunaan Internal** (jika ada data)
| ID | Tanggal | Waktu | Item ID | Quantity | Harga Satuan | Total Amount | Alasan | Kang Ider |
|----|---------|-------|---------|----------|--------------|--------------|--------|-----------|
| 1 | 23/07/2025 | 15:00 | 4 | 2 | 8000 | 16000 | Konsumsi | Ahmad |

## 🔧 Implementasi Teknis

### Dependencies
```json
{
  "xlsx": "^0.18.5",
  "@types/xlsx": "^0.0.36"
}
```

### Fungsi Export
```typescript
const exportToExcel = () => {
  // Membuat workbook baru
  const workbook = XLSX.utils.book_new()
  
  // Membuat sheet untuk setiap jenis data
  // 1. Summary sheet
  // 2. Penjualan sheet  
  // 3. Menu & Stock sheet
  // 4. Pengeluaran Operasional sheet (conditional)
  // 5. Penggunaan Internal sheet (conditional)
  
  // Generate filename dengan format tanggal
  const filename = `Laporan_Dashboard_${format(dateFrom, "yyyy-MM-dd")}_${format(dateTo, "yyyy-MM-dd")}.xlsx`
  
  // Save file
  XLSX.writeFile(workbook, filename)
}
```

## 🎨 UI/UX Design

### Button Styling
```css
className="h-9 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
```

### Icon
- **Lucide Icon**: `Download`
- **Size**: 16px (h-4 w-4)
- **Position**: Sebelum text dengan margin right

## 📊 Data Processing

### 1. **Filter Integration**
- Export menggunakan data yang sudah difilter (`filteredPenjualan`, `filteredOperationalExpenses`, dll)
- Respects date range dan kangider selection

### 2. **Data Transformation**
- Convert data ke format array untuk XLSX
- Format tanggal ke format Indonesia (dd/MM/yyyy)
- Format currency dengan separator ribuan
- Handle null/undefined values

### 3. **Conditional Sheets**
- Sheet Pengeluaran Operasional hanya dibuat jika ada data
- Sheet Penggunaan Internal hanya dibuat jika ada data

## 🚀 Cara Penggunaan

### Untuk User:
1. Login ke dashboard
2. Set filter tanggal dan kangider sesuai kebutuhan
3. Klik tombol "Export Excel" di header
4. File akan otomatis ter-download
5. Buka file Excel untuk melihat laporan lengkap

### Untuk Developer:
1. Install dependencies: `npm install xlsx @types/xlsx`
2. Import XLSX library
3. Implementasi fungsi `exportToExcel()`
4. Tambahkan button dengan onClick handler
5. Test dengan berbagai skenario data

## 🔍 Error Handling

### Try-Catch Block
```typescript
try {
  // Export logic
  XLSX.writeFile(workbook, filename)
} catch (error) {
  console.error('Error exporting to Excel:', error)
  alert('Terjadi kesalahan saat export Excel. Silakan coba lagi.')
}
```

### Validasi Data
- Check apakah data tersedia sebelum export
- Handle empty arrays gracefully
- Validate date ranges

## 📱 Responsive Design
- Button menyesuaikan dengan ukuran layar
- Text dan icon tetap proporsional
- Accessible di mobile dan desktop

## 🎯 Benefits

### Untuk Business:
1. **Laporan Lengkap** - Semua data dalam satu file
2. **Format Profesional** - Siap untuk presentasi
3. **Data Terstruktur** - Mudah untuk analisis lanjutan
4. **Backup Data** - Arsip laporan periode tertentu

### Untuk User:
1. **One-Click Export** - Mudah dan cepat
2. **Multi-Sheet Organization** - Data terorganisir
3. **Filename Otomatis** - Tidak perlu rename
4. **Excel Compatible** - Bisa dibuka di semua aplikasi spreadsheet

## 🔄 Future Enhancements
1. **Custom Date Format** - Pilihan format tanggal
2. **Template Selection** - Multiple template export
3. **Email Integration** - Kirim laporan via email
4. **Scheduled Export** - Export otomatis berkala
5. **PDF Export** - Alternative format export
