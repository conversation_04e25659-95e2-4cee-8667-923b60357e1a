# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next
out

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed

# Coverage directory used by tools like istanbul
coverage

# Documentation dan troubleshooting files
*.md
README*
TROUBLESHOOTING*
CLEANUP*
FIELD*
PERMISSION*
PRACTICAL*
QUICK*
ADMIN* 