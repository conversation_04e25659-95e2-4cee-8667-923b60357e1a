# Coffee Dashboard - Ider Kopi

Dashboard untuk monitoring penjualan dan stock kopi dari Kang Ider.

## Masalah yang Diperbaiki

### 1. Data Dashboard Tidak Muncul

**Penyebab:**
- API endpoint memerlukan autentikasi yang valid
- Filter tanggal mungkin tidak sesuai dengan data yang tersedia
- Response API mungkin kosong untuk rentang tanggal tertentu
- Error handling yang kurang informatif
- **Field name salah**: `harga_produk` seharusnya `harga`

**Solusi yang Diterapkan:**

1. **Logging yang Lebih Detail**
   - Menambahkan console.log untuk setiap API call
   - Menampilkan URL lengkap yang dipanggil
   - Logging response status dan error

2. **Improved Error Handling**
   - Menampilkan pesan error yang lebih informatif
   - Tombol reload untuk mencoba ulang
   - Loading state yang lebih baik

3. **Default Date Range**
   - Mengubah default dari hari ini saja menjadi 7 hari terakhir
   - Menambahkan tombol "30 Hari Terakhir" untuk testing

4. **Debug Information**
   - Panel debug di development mode
   - Menampilkan jumlah data yang berhasil dimuat

5. **Demo Mode**
   - Mode demo dengan data contoh untuk testing
   - Membantu memverifikasi bahwa UI berfungsi dengan benar

6. **Better UX**
   - Pesan informatif ketika tidak ada data
   - Loading spinner yang lebih menarik
   - Informasi jumlah transaksi di setiap card

7. **Field Name Correction**
   - Memperbaiki field `harga_produk` menjadi `harga` sesuai database schema
   - Update API query dan TypeScript types
   - Fix semua referensi di calculation logic

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3001](http://localhost:3001) with your browser

## Troubleshooting

### Jika Data Tidak Muncul:

1. **Cek Login Credentials**
   - Pastikan email dan password benar
   - Cek di browser console untuk error autentikasi

2. **Coba Rentang Tanggal Berbeda**
   - Gunakan tombol "30 Hari Terakhir"
   - Pilih rentang tanggal yang lebih luas

3. **Gunakan Mode Demo**
   - Klik tombol "Mode Demo" untuk melihat data contoh
   - Verifikasi bahwa UI berfungsi dengan benar

4. **Cek Browser Console**
   - Buka Developer Tools (F12)
   - Lihat tab Console untuk error atau log
   - Cek tab Network untuk status API calls

5. **Cek Debug Panel**
   - Di development mode, lihat panel debug di atas
   - Verifikasi jumlah data yang dimuat

### Jika Muncul Error "Body is disturbed or locked":

1. **Refresh Halaman** - Tekan F5 atau Ctrl+R
2. **Clear Cache** - Gunakan tombol "Clear Cache & Reload" di error screen
3. **Mode Demo** - Gunakan tombol "Coba Mode Demo" untuk testing
4. **Hard Refresh** - Tekan Ctrl+Shift+R (Windows) atau Cmd+Shift+R (Mac)

> **Catatan:** Error ini sudah diperbaiki di kode dengan menggunakan `response.clone()`, tapi bisa masih muncul karena browser cache.

### Jika Muncul Error 403 (Access Denied):

1. **Hubungi Administrator** - User tidak memiliki permission untuk mengakses data
2. **Login Ulang** - Gunakan tombol "🔐 Login Ulang" di error screen
3. **Coba Akun Lain** - Login dengan akun yang memiliki permission lebih tinggi
4. **Mode Demo** - Gunakan untuk testing UI tanpa akses API
5. **Lihat PERMISSION_GUIDE.md** - Panduan lengkap untuk administrator

> **Catatan:** Error 403 adalah masalah permission, bukan masalah teknis. Solusinya adalah memberikan akses yang tepat kepada user.

### Jika User Sudah Admin tapi Tetap Error 403:

1. **Cek Browser Console** - Buka F12 dan lihat log detail
2. **Test Endpoints** - Gunakan tombol "🔍 Test Endpoints" (development mode)
3. **Verifikasi Token** - Pastikan token mengandung permission yang benar
4. **Lihat ADMIN_403_DEBUG.md** - Panduan debugging khusus untuk admin
5. **Test Manual** - Gunakan curl/Postman dengan token yang sama

> **Kemungkinan:** Token valid tapi permission mapping di backend bermasalah.

## ✅ Status: Production Ready

Dashboard telah berhasil diperbaiki dan dibersihkan:

### Masalah yang Telah Diselesaikan:
- ✅ **Field name error**: `harga_produk` → `harga`
- ✅ **API permission**: Error 403 teratasi
- ✅ **Data loading**: Dashboard menampilkan data real
- ✅ **Code cleanup**: Menghapus demo mode dan debug info

### Fitur yang Berfungsi:
- ✅ Login dengan role admin
- ✅ Data penjualan real dari API
- ✅ Filter berdasarkan tanggal dan Kang Ider
- ✅ Perhitungan total Cash dan QRIS
- ✅ Tabel data menu dengan stock
- ✅ Peta lokasi Kang Ider
- ✅ Responsive design

> **Dashboard siap untuk production deployment.**

### API Endpoints

- **Penjualan:** `https://api.iderkopi.id/items/penjualan`
- **Stock Harian:** `https://api.iderkopi.id/items/stock_harian`
- **Lokasi Kangider:** `https://api.iderkopi.id/items/lokasi_kangider`
- **Kangider:** `https://api.iderkopi.id/items/kangider`

## Features

- 📊 Dashboard penjualan real-time
- 💰 Tracking pembayaran Cash dan QRIS
- 📦 Monitoring stock harian
- 🗺️ Peta lokasi berjualan
- 📅 Filter berdasarkan tanggal dan Kang Ider
- 🔍 Mode debug untuk troubleshooting
- 🎭 Mode demo untuk testing

## Tech Stack

- Next.js 14
- React 18
- TypeScript
- Tailwind CSS
- Radix UI Components
- Mapbox GL JS
- Recharts

## Deployment

```bash
npm run build
npm run start
```

Server akan berjalan di port yang ditentukan oleh environment variable `PORT` atau default 10000.
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.
