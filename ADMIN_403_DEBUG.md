# Debug Guide - Error 403 dengan Role Admin

## 🚨 Situasi
User sudah memiliki **role admin** tetapi masih mendapat **error 403** saat mengakses dashboard.

## 🔍 Kemungkinan Penyebab

### 1. **Token Issues**
- Token expired meskipun baru login
- Token tidak mengandung permission yang benar
- Token format tidak sesuai dengan yang diharapkan API

### 2. **API Configuration**
- Endpoint memerlukan permission spesifik selain role admin
- API menggunakan permission-based access control (PBAC) bukan role-based (RBAC)
- Whitelist IP atau domain restrictions

### 3. **Database/Backend Issues**
- Role admin tidak memiliki mapping permission yang benar
- Permission cache belum di-refresh
- Database permission table tidak sinkron

### 4. **Request Issues**
- Headers tidak lengkap atau salah format
- Content-Type tidak sesuai
- CORS issues

## 🛠️ Langkah Debugging

### Step 1: Verifikasi Token
1. **Buka Browser Console** (F12)
2. **Login** dan lihat log:
   ```
   Login response data: { ... }
   User info: { id, email, role, permissions }
   Token preview: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```
3. **Cek apakah role = "admin"** dan permissions ada

### Step 2: Test Token Validity
1. **Klik tombol "🔍 Test Endpoints"** (hanya di development mode)
2. **Lihat hasil test** di console:
   ```
   Testing Users/Me: 200 OK
   Testing Kangider: 403 Forbidden
   Testing Penjualan: 403 Forbidden
   ```
3. **Identifikasi endpoint mana yang bermasalah**

### Step 3: Manual API Test
```bash
# Test dengan curl
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     https://api.iderkopi.id/users/me

curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     https://api.iderkopi.id/items/kangider?limit=5
```

### Step 4: Cek Response Detail
1. **Buka Network Tab** di Developer Tools
2. **Refresh dashboard** setelah login
3. **Lihat API calls** yang gagal
4. **Cek response body** untuk detail error

## 🔧 Solusi Berdasarkan Penyebab

### Jika Token Valid tapi Permission Salah:
```sql
-- Cek permission mapping untuk role admin
SELECT r.name as role_name, p.name as permission_name 
FROM roles r 
JOIN role_permissions rp ON r.id = rp.role_id 
JOIN permissions p ON rp.permission_id = p.id 
WHERE r.name = 'admin';

-- Pastikan ada permission untuk:
-- - read:penjualan
-- - read:stock_harian  
-- - read:lokasi_kangider
-- - read:kangider
```

### Jika API Configuration Issue:
1. **Cek API documentation** untuk permission requirements
2. **Hubungi backend developer** untuk verifikasi endpoint permissions
3. **Test dengan Postman/Insomnia** menggunakan token yang sama

### Jika Database Issue:
```sql
-- Refresh permission cache
REFRESH MATERIALIZED VIEW user_permissions;

-- Atau restart API service
sudo systemctl restart api-service
```

## 🎯 Quick Fixes

### Fix 1: Re-login dengan Clear Cache
```javascript
// Di browser console
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### Fix 2: Test dengan Super Admin
- Login dengan akun super admin
- Jika berhasil, berarti permission mapping bermasalah
- Jika tetap gagal, berarti API configuration issue

### Fix 3: Bypass untuk Testing
```javascript
// Temporary fix - gunakan demo mode
// Klik tombol "🎭 Coba Mode Demo"
```

## 📊 Expected API Responses

### Successful Response:
```json
{
  "data": [...],
  "meta": {
    "total_count": 123,
    "filter_count": 123
  }
}
```

### Error 403 Response:
```json
{
  "errors": [
    {
      "message": "You don't have permission to access this.",
      "extensions": {
        "code": "FORBIDDEN"
      }
    }
  ]
}
```

## 🚀 Monitoring & Logging

### Enable Detailed Logging:
1. **Buka dashboard.tsx**
2. **Lihat console logs** untuk:
   - Login response
   - Token info
   - Request headers
   - API responses

### Key Logs to Watch:
```
✅ Login successful, token set
✅ User info: {role: "admin", permissions: [...]}
✅ Token test response status: 200
❌ Penjualan API Error: 403 Forbidden
```

## 📞 Escalation Path

### Level 1: Frontend Debug
- Clear cache dan re-login
- Test dengan demo mode
- Cek browser console

### Level 2: API Debug  
- Test endpoints dengan curl/Postman
- Cek API logs
- Verify token dengan /users/me

### Level 3: Backend Debug
- Cek database permissions
- Verify role mappings
- Check API configuration

### Level 4: Infrastructure
- Cek load balancer rules
- Verify SSL certificates
- Check firewall rules

---

**Catatan:** Jika semua langkah di atas sudah dicoba dan masih error 403, kemungkinan besar ada masalah di backend/database yang perlu ditangani oleh developer backend.
