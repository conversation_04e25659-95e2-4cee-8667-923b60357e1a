# Troubleshooting Guide - Coffee Dashboard

## Ma<PERSON>ah <PERSON>um dan <PERSON>

### 1. Data Dashboard Tidak Muncul (Total = 0)

**Gejala:**
- Semua card menampilkan nilai 0
- Tabel kosong
- Peta tidak menampilkan marker

**Kemungkinan Penyebab:**

#### A. <PERSON><PERSON><PERSON>
- **Cek:** Login credentials salah
- **Sol<PERSON>i:** 
  - Pastikan email dan password benar
  - Coba login ulang
  - Cek di browser console untuk error 401/403

#### B. Rentang Tanggal Tidak Sesuai
- **Cek:** Data tidak ada untuk tanggal yang dipilih
- **Solusi:**
  - Gunakan tombol "30 Hari Terakhir"
  - Pilih rentang tanggal yang lebih luas
  - Coba tanggal bulan sebelumnya

#### C. Filter Kang Ider
- **Cek:** Kang Ider yang dipilih tidak memiliki data
- **Solusi:**
  - <PERSON><PERSON><PERSON> "<PERSON><PERSON><PERSON>"
  - <PERSON><PERSON> Kang Ider yang berbeda

#### D. Masalah API
- **Cek:** API endpoint tidak merespons
- **Solusi:**
  - Cek koneksi internet
  - Lihat browser console untuk error network
  - Cek status API di https://api.iderkopi.id

### 2. Error Saat Login

**Gejala:**
- Pesan "Login failed"
- Tidak bisa masuk ke dashboard

**Solusi:**
1. Pastikan email format benar
2. Cek password (case sensitive)
3. Cek koneksi internet
4. Coba refresh halaman

### 3. Peta Tidak Muncul

**Gejala:**
- Area peta kosong atau error
- Marker tidak tampil

**Solusi:**
1. Cek Mapbox token di dashboard.tsx line 17
2. Pastikan ada data lokasi
3. Cek browser console untuk error Mapbox

### 4. Loading Terus Menerus

**Gejala:**
- Spinner loading tidak hilang
- Data tidak pernah muncul

**Solusi:**
1. Refresh halaman
2. Cek browser console untuk error
3. Coba mode demo untuk test UI
4. Clear browser cache

## Tools untuk Debugging

### 1. Browser Developer Tools

**Cara Buka:** Tekan F12 atau klik kanan → Inspect

**Tab Console:**
- Lihat error messages
- Cek log API calls
- Verifikasi data yang diterima

**Tab Network:**
- Monitor API requests
- Cek status code (200, 401, 404, 500)
- Lihat response data

### 2. Debug Panel (Development Mode)

Ketika menjalankan `npm run dev`, akan muncul panel debug yang menampilkan:
- Jumlah data penjualan
- Jumlah data stock
- Jumlah data lokasi
- Jumlah Kang Ider

### 3. Mode Demo

Gunakan tombol "Mode Demo" untuk:
- Test UI dengan data contoh
- Verifikasi bahwa komponen berfungsi
- Isolasi masalah (UI vs API)

## Langkah Debugging Sistematis

### Step 1: Verifikasi UI
1. Aktifkan Mode Demo
2. Pastikan semua komponen tampil dengan benar
3. Jika UI bermasalah, fokus ke perbaikan komponen

### Step 2: Verifikasi Autentikasi
1. Matikan Mode Demo
2. Login dengan credentials yang benar
3. Cek browser console untuk error auth

### Step 3: Verifikasi API Calls
1. Buka Developer Tools → Network tab
2. Refresh halaman
3. Lihat API calls ke api.iderkopi.id
4. Cek status code dan response

### Step 4: Verifikasi Data
1. Lihat Debug Panel
2. Coba rentang tanggal berbeda
3. Coba filter Kang Ider berbeda

### Step 5: Verifikasi Filter
1. Pastikan dateRange.from dan dateRange.to valid
2. Cek selectedKangIder value
3. Verifikasi logic filtering

## Contoh Error dan Solusi

### Error: "Cannot read property 'id' of undefined"
**Penyebab:** Data structure tidak sesuai ekspektasi
**Solusi:** Cek response API, tambahkan null checking

### Error: "Network Error"
**Penyebab:** Koneksi internet atau API down
**Solusi:** Cek koneksi, coba lagi nanti

### Error: "401 Unauthorized"
**Penyebab:** Token expired atau invalid
**Solusi:** Login ulang

### Error: "403 Forbidden/Access Denied"
**Penyebab:** User tidak memiliki permission untuk mengakses data
**Solusi:**
1. Hubungi administrator untuk memberikan akses
2. Pastikan akun memiliki role yang sesuai
3. Coba login dengan akun yang berbeda
4. Gunakan mode demo untuk testing

### Error: "CORS Error"
**Penyebab:** Browser blocking cross-origin request
**Solusi:** Pastikan API mendukung CORS atau gunakan proxy

### Error: "Body is disturbed or locked"
**Penyebab:** Response body sudah dibaca/di-consume sebelumnya
**Solusi:**
1. Refresh halaman (F5 atau Ctrl+R)
2. Clear browser cache
3. Restart development server
4. Gunakan response.clone() sebelum membaca body (sudah diperbaiki)

## Kontak Support

Jika masalah masih berlanjut:
1. Screenshot error message
2. Copy log dari browser console
3. Catat langkah yang menyebabkan error
4. Hubungi developer dengan informasi tersebut
