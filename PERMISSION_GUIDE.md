# Permission Guide - Error 403 Access Denied

## 🚫 Error yang <PERSON>
```
⚠️ Terjadi Ke<PERSON>ahan
Terjadi kesalahan saat mengambil data: Access denied (403): User tidak memiliki permission untuk mengakses data penjualan. Hubungi administrator untuk member<PERSON>n akses.
```

## 📋 Apa itu Error 403?

Error 403 (Forbidden/Access Denied) berarti:
- ✅ Login berhasil (token valid)
- ❌ User tidak memiliki permission untuk mengakses resource tertentu
- 🔐 Server menolak akses karena insufficient privileges

## 🔍 Penyebab Umum

### 1. Role/Permission Tidak Sesuai
- User memiliki role yang tidak memiliki akses ke data penjualan
- Permission untuk endpoint tertentu tidak diberikan
- Akun baru yang belum dikonfigurasi

### 2. Konfigurasi API
- Endpoint memerlukan permission khusus
- Role-based access control (RBAC) aktif
- User tidak dalam group yang tepat

### 3. Data Scope Limitation
- User hanya bisa akses data tertentu
- Geographical atau departmental restrictions
- Time-based access limitations

## 🛠️ Solusi

### 1. Hubungi Administrator
**Yang perlu dilakukan administrator:**
- Cek role user di sistem
- Berikan permission untuk:
  - `items/penjualan` (data penjualan)
  - `items/stock_harian` (data stock)
  - `items/lokasi_kangider` (data lokasi)
  - `items/kangider` (data kangider)

### 2. Verifikasi Role User
**Role yang biasanya diperlukan:**
- `Dashboard Viewer`
- `Sales Manager`
- `Admin`
- `Supervisor`

### 3. Cek Permission Spesifik
**Permission yang diperlukan:**
- `read:penjualan`
- `read:stock_harian`
- `read:lokasi_kangider`
- `read:kangider`

### 4. Alternative Solutions
1. **Gunakan Mode Demo**
   - Klik tombol "🎭 Coba Mode Demo"
   - Test UI tanpa akses API

2. **Login dengan Akun Lain**
   - Gunakan akun dengan permission lebih tinggi
   - Test dengan akun admin/supervisor

3. **Request Access**
   - Submit request ke IT/Admin
   - Jelaskan kebutuhan akses dashboard

## 📞 Informasi untuk Administrator

### API Endpoints yang Diakses:
```
GET /items/penjualan
GET /items/stock_harian  
GET /items/lokasi_kangider
GET /items/kangider
```

### Headers yang Digunakan:
```
Authorization: Bearer <token>
Content-Type: application/json
```

### Permission yang Diperlukan:
- Read access ke collection `penjualan`
- Read access ke collection `stock_harian`
- Read access ke collection `lokasi_kangider`
- Read access ke collection `kangider`

### Filter yang Digunakan:
- Date range filtering
- Kangider filtering
- Field selection untuk optimasi

## 🔧 Debugging untuk Admin

### 1. Cek User Role
```sql
-- Contoh query untuk cek role user
SELECT user_id, role, permissions 
FROM user_roles 
WHERE user_id = '<user_id>';
```

### 2. Cek Permission Mapping
```sql
-- Contoh query untuk cek permission
SELECT role, resource, action 
FROM role_permissions 
WHERE role = '<user_role>';
```

### 3. Test API Access
```bash
# Test dengan curl
curl -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     https://api.iderkopi.id/items/penjualan
```

## 🎯 Quick Actions

### Untuk User:
1. ✅ Klik tombol "🔐 Login Ulang"
2. ✅ Coba "🎭 Mode Demo"
3. ✅ Hubungi administrator
4. ✅ Gunakan akun dengan permission lebih tinggi

### Untuk Administrator:
1. ✅ Cek role user di admin panel
2. ✅ Berikan permission yang diperlukan
3. ✅ Test akses setelah perubahan
4. ✅ Dokumentasikan permission requirements

## 📚 Resources

- **Admin Panel:** [URL admin panel]
- **Permission Documentation:** [URL dokumentasi]
- **Contact IT Support:** [Email/Phone IT]
- **User Management Guide:** [URL guide]

## 🔄 Testing Checklist

Setelah permission diperbaiki:
- [ ] User bisa login
- [ ] Data penjualan muncul
- [ ] Data stock muncul
- [ ] Data lokasi muncul
- [ ] Filter berfungsi
- [ ] Export/download berfungsi (jika ada)

---

**Catatan:** Error 403 adalah masalah permission, bukan masalah teknis. Solusinya adalah memberikan akses yang tepat kepada user.
